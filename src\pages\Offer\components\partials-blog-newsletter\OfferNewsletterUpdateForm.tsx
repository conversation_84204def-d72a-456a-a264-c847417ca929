import HtmlEditor from "@/components/HtmlEditor";
import SProFormDigit from "@/components/SProFormDigit";
import { LS_TOKEN_NAME } from "@/constants";
import { deleteFile } from "@/services/app/File/file";
import { updateOrCreateOfferBlogByOfferNo } from "@/services/app/Offer/offer-blog";
import Util, { sn } from "@/util";
import {
  ProColumns,
  ProForm,
  ProFormInstance,
  ProFormText,
  ProFormUploadDragger,
  EditableProTable,
  ProFormDigitProps,
} from "@ant-design/pro-components";
import { Button, message, Modal, Popconfirm, Space } from "antd";
import { RcFile } from "antd/es/upload";
import { useCallback, useRef, useState } from "react";

type FormValueType = API.OfferBlog;

export type OfferNewsletterUpdateFormProps = {
  offer_no: string;
  onSubmit?: (formData: FormValueType) => void;
};

const OfferNewsletterUpdateForm: React.FC<OfferNewsletterUpdateFormProps> = ({ offer_no, onSubmit }) => {
  const formRef = useRef<ProFormInstance<FormValueType>>();

  const [loading, setLoading] = useState<boolean>(false);

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<API.OfferNewsletter[]>([]);

  const actionRef = useRef();

  // Handle saving changes
  const handleSave = useCallback(async () => {
    try {
      // await onSave(dataSource);
      message.success("Newsletters updated successfully");
    } catch (error) {
      message.error("Failed to update newsletters");
      console.error("Error saving newsletters:", error);
    }
  }, []);

  // Handle adding new newsletter
  const handleAddNewsletter = () => {
    const newNewsletter: API.OfferNewsletter = {
      id: Date.now(), // Temporary ID for new items
    };

    setDataSource([...dataSource, newNewsletter]);
    setEditableRowKeys([...editableKeys, newNewsletter.id!]);

    return newNewsletter;
  };

  // Handle deleting newsletter
  const handleDeleteNewsletter = (record: API.OfferNewsletter) => {
    const newDataSource = dataSource.filter((item) => item.id !== record.id);
    setDataSource(newDataSource);
    setEditableRowKeys(editableKeys.filter((key) => key !== record.id));
    message.success("Newsletter deleted");
  };

  // Define columns for the ProEditableTable
  const columns: ProColumns[] = [
    {
      title: "Product",
      dataIndex: "product_title",
      key: "product_title",
      width: 200,
      formItemProps: {
        rules: [
          {
            required: true,
            message: "Newsletter title is required",
          },
        ],
      },
    },
    {
      title: "Pcs / Case",
      dataIndex: "case_qty",
      key: "case_qty",
      width: 100,
      valueType: "digit",
      formItemProps: {
        style: { marginBottom: 0 },
      },
      fieldProps: { precision: 0 },
      renderFormItem(schema, config, form, action) {
        return <SProFormDigit allowClear formItemProps={{ ...schema.formItemProps }} />;
      },
    },
    {
      title: "Cases / Pallet",
      dataIndex: "ve_pallet",
      key: "ve_pallet",
      width: 100,
      valueType: "digit",
      formItemProps: {
        style: { marginBottom: 0 },
      },
      fieldProps: { precision: 0 },
      renderFormItem(schema, config, form, action) {
        return <SProFormDigit allowClear formItemProps={{ ...schema.formItemProps }} />;
      },
    },
    {
      title: "Price",
      dataIndex: "price",
      key: "price",
      width: 100,
      valueType: "digit",
      formItemProps: {
        style: { marginBottom: 0 },
      },
      renderFormItem(schema, config, form, action) {
        return <SProFormDigit allowClear formItemProps={{ ...schema.formItemProps }} fieldProps={{ precision: 3 }} />;
      },
    },
    {
      title: "Pic",
      dataIndex: "files",
      key: "files",
      width: 200,
      valueType: "text",
      renderFormItem(schema, config, form, action) {
        return (
          <ProFormUploadDragger
            description="Please select newsletter files or drag & drop"
            wrapperCol={{ span: 24 }}
            // accept="image/*"
            fieldProps={{
              multiple: true,
              listType: "picture",
              name: "file",
              style: { marginBottom: 24 },
              headers: {
                Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
              },
              beforeUpload: (file: RcFile, fileList: RcFile[]) => {
                return false;
              },
              onRemove: async (file: API.File) => {
                if (file.id) {
                  const { confirm } = Modal;
                  return new Promise((resolve, reject) => {
                    confirm({
                      title: "Are you sure you want to delete?",
                      onOk: async () => {
                        resolve(true);
                        const hide = message.loading(`Deleting a file '${file.file_name}'.`, 0);
                        const res = await deleteFile(sn(file.id));
                        hide();
                        if (res) {
                          message.success(`Deleted successfully!`);
                        } else {
                          Util.error(`Delete failed, please try again!`);
                        }

                        return res;
                      },
                      onCancel: () => {
                        reject(true);
                      },
                    });
                  });
                } else {
                  return true;
                }
              },
            }}
          />
        );
      },
    },

    {
      title: "Actions",
      valueType: "option",
      width: 100,
      render(dom, entity, index, action, schema) {
        return [
          <a
            key="edit"
            onClick={() => {
              // action?.startEditable?.(record.id!);
            }}
          >
            Edit
          </a>,
          <Popconfirm
            key="delete"
            title="Are you sure you want to delete this newsletter?"
            // onConfirm={() => handleDeleteNewsletter(record)}
            okText="Yes"
            cancelText="No"
          >
            <a style={{ color: "red" }}>Delete</a>
          </Popconfirm>,
        ];
      },
    },
  ];

  return (
    <div style={{ width: "100%" }}>
      <Space direction="vertical" style={{ width: "100%" }}>
        <EditableProTable<API.OfferNewsletter>
          actionRef={actionRef}
          rowKey="id"
          headerTitle={false}
          maxLength={50}
          recordCreatorProps={{
            record(index, dataSource) {
              const newNewsletter: API.OfferNewsletter = {
                id: Date.now(), // Temporary ID for new items
              };

              return newNewsletter;
            },
          }}
          columns={columns}
          value={dataSource}
          onChange={setDataSource as any}
          editable={{
            type: "multiple",
            editableKeys,
            onSave: async (rowKey, data) => {
              console.log("Saving row:", rowKey, data);
              return true;
            },
            onDelete: async (key) => {
              const newDataSource = dataSource.filter((item) => item.id !== key);
              setDataSource(newDataSource);
              return true;
            },
            onChange: setEditableRowKeys,
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
          pagination={{
            pageSize: 100,
            hideOnSinglePage: true,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          scroll={{ x: 1000 }}
          size="small"
        />
        <Space style={{ width: "100%", justifyContent: "flex-end" }}>
          <Button
            type="default"
            onClick={() => {
              formRef.current?.resetFields();
            }}
          >
            Reset
          </Button>
          <Button
            type="primary"
            onClick={() => {
              const hide = message.loading("Saving blog data...", 0);
              updateOrCreateOfferBlogByOfferNo({ offer_no, ...formRef.current?.getFieldsValue() })
                .then((res) => {
                  hide();
                  message.success("Saved successfully.");
                  if (onSubmit) onSubmit(res);
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
          >
            Save
          </Button>
        </Space>
      </Space>
    </div>
  );
};
export default OfferNewsletterUpdateForm;
