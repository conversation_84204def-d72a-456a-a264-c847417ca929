import { request } from 'umi';
import { paramsSerializer } from '../api';
import { RequestConfig } from '@umijs/max';
import { RequestOptionsType } from '@ant-design/pro-components';

const urlPrefix = '/api/offer/newsletter';

/**
 * Get OfferNewsletters list
 *
 * GET /api/offer/newsletter
 */
export async function getOfferNewsletterListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultList<API.OfferNewsletter>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

export async function getOfferNewsletter(id?: number, params?: API.OfferNewsletter & API.PageParams) {
  return getOfferNewsletterListByPage({ id, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}


export async function getOfferNewsletterByOfferNo(offerNo?: number | string, params?: API.OfferNewsletter & API.PageParams) {
  return getOfferNewsletterListByPage({ offer_no: offerNo, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}

/**
 * Create supplier.
 *
 *  POST /api/offer/newsletter */
export async function addOfferNewsletter(
  data?: API.OfferNewsletter | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferNewsletter>>(`${urlPrefix}`, config).then((res) => res.message);
}

/**
 * Update OfferNewsletter data.
 *
 *  PUT /api/offer/newsletter/{id}/update */
export async function updateOfferNewsletter(
  id?: number,
  data?: API.OfferNewsletter | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferNewsletter>>(`${urlPrefix}/${id}/update`, config).then(
    (res) => res.message,
  );
}


/**
 * Update or Create Offer Newsletters.
 *
 *  POST /api/offer/newsletter/updateOrCreate */
export async function updateOrCreateOfferNewsletterByOfferNo(
  data?: API.OfferNewsletter | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferNewsletter>>(`${urlPrefix}/updateOrCreate`, config).then((res) => res.message);
}


/**
 * Get AC List dropdown list.
 *
 * GET /api/offer/newsletter/getOfferNewsletterACList
 */
export async function getOfferNewsletterACList(params: API.PageParams) {
  return request<API.ResultObject<RequestOptionsType[]>>(`${urlPrefix}/getOfferNewsletterACList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: 500,
      page: params.current,
      sort: { id: 'descend' },
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** delete DELETE /api/offer/newsletter/{id} */
export async function deleteOfferNewsletter(id?: string | number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}
